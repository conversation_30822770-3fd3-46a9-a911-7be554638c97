# Rebuild the api container: docker compose up -d --build --no-deps api

FROM python:3.12-slim-bullseye

# Install for cv2
RUN apt-get update && apt-get install ffmpeg libsm6 libxext6 -y

WORKDIR /app

# Copy requirements file
COPY requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
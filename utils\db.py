"""
Database utilities for the API.
"""

import psycopg2
import psycopg2.extras
from utils.config import POSTGRES_HOST, POSTGRES_PORT, POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DB

def get_db_connection():
    """
    Get a connection to the PostgreSQL database.
    
    Returns:
        A connection to the PostgreSQL database.
    """
    conn = psycopg2.connect(
        host=POSTGRES_HOST,
        port=POSTGRES_PORT,
        user=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        dbname=POSTGRES_DB
    )
    conn.autocommit = True
    return conn

def get_ip_asset_metadata(asset_ids):
    """
    Get metadata for IP assets from PostgreSQL.
    
    Args:
        asset_ids: A list of IP asset IDs.
        
    Returns:
        A dictionary mapping IP asset IDs to their metadata.
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
    
    # Convert asset_ids to a list of UUIDs
    uuids = list(asset_ids)
    
    # Query trademarks
    cursor.execute(
        "SELECT * FROM trademarks WHERE id = ANY(%s::uuid[])",
        (uuids,)
    )
    trademarks = {str(row['id']): dict(row) for row in cursor.fetchall()}
    
    # Query patents
    cursor.execute(
        "SELECT * FROM patents WHERE id = ANY(%s::uuid[])",
        (uuids,)
    )
    patents = {str(row['id']): dict(row) for row in cursor.fetchall()}
    
    # Query copyrights
    cursor.execute(
        "SELECT * FROM copyrights WHERE id = ANY(%s::uuid[])",
        (uuids,)
    )
    copyrights = {str(row['id']): dict(row) for row in cursor.fetchall()}
    
    # Close connection
    cursor.close()
    conn.close()
    
    # Combine all metadata
    metadata = {}
    metadata.update({id: {"type": "trademark", "data": data} for id, data in trademarks.items()})
    metadata.update({id: {"type": "patent", "data": data} for id, data in patents.items()})
    metadata.update({id: {"type": "copyright", "data": data} for id, data in copyrights.items()})
    
    return metadata


def get_reverse_check_results_by_client_id_and_date(client_id, date):
    """
    Get all records from the reverse_check_result table for a given client_id and date.
    Args:
        client_id: The client identifier to filter by.
        date: The date to filter by (string, e.g., '2024-06-15').
    Returns:
        A list of row dicts from reverse_check_result.
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
    cursor.execute("SELECT * FROM reverse_check_result WHERE client_id = %s AND DATE(create_time) = %s", (client_id, date))
    results = [dict(row) for row in cursor.fetchall()]
    cursor.close()
    conn.close()
    return results
